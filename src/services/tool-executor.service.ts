import {injectable, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {ImageRepository, MessageRepository} from '../repositories';
import {ShopifyApiInvoker} from './shopify/shopify-api-invoker.service';
import {ChatService} from './chat/chat.service';
import {EcommerceMetricController} from '../controllers/ecommerce-metric.controller';
import {AdminUiController} from '../controllers/admin-ui.controller';
import {OpenAiService} from './open-ai.service';
import NodeCache from 'node-cache';
import Fuse from 'fuse.js';
import { calculateAspectRatio } from '../utils/aspectRatioUtils';

const fetch = require('node-fetch');

// Flux1 Kontext API Key
const BFL_API_KEY = '66a5b63e-cd84-4c24-8977-34b6d0ae8c2b';

let DATA_API: string = process.env.ECOMMERCE_AWS_URL! || "nq2mcp0pfh.execute-api.us-east-1.amazonaws.com";

@injectable()
export class ToolExecutorService {

  // Initialize NodeCache for product, metric, and data caching (TTL: 5 minutes, max 1000 keys)
  private readonly productCache = new NodeCache({
    stdTTL: 300,
    checkperiod: 60,
    maxKeys: 1000,
    deleteOnExpire: true,
    useClones: false // Better memory performance
  });

  private readonly metricCache = new NodeCache({
    stdTTL: 300,
    checkperiod: 60,
    maxKeys: 1000,
    deleteOnExpire: true,
    useClones: false // Better memory performance
  });

  constructor(
    @repository(ImageRepository)
    private imageRepository: ImageRepository,
    @repository(MessageRepository)
    private messageRepository: MessageRepository,
    @service(ShopifyApiInvoker)
    private shopifyApiInvoker: ShopifyApiInvoker,
    @service(ChatService)
    private chatService: ChatService,
    @service(OpenAiService)
    private openAiService: OpenAiService,
  ) {}

  /**
   * Get the tool executor functions
   */
  getToolExecutors(): {[functionName: string]: (args: any) => Promise<any>} {
    return {
      'product_lookup': async (params: any): Promise<any> => {
        console.log('PRODUCT LOOKUP TOOL CALLED WITH:', params);
        console.log('PRODUCT LOOKUP TOOL - orgId:', params.orgId);

        try {
          const query = params.query || '';
          const orgId = params.orgId || 0;
          console.log('PRODUCT LOOKUP TOOL - using orgId:', orgId);

          // Check if Shopify is connected for this organization
          const isShopifyConnected = await this.isShopifyConnected(orgId);

          if (!isShopifyConnected) {
            return {
              status: 'success',
              products: [],
              message: 'No products found - Shopify not connected'
            };
          }

          // Fetch products from Shopify via our API
          const products = await this.fetchProductsFromShopify(orgId);

          if (!products || products.length === 0) {
            return {
              status: 'success',
              products: [],
              message: 'No products found in store'
            };
          }

          // Filter products by query if provided
          let filteredProducts = products;
          if (query && query.trim() !== '') {
            // Use Fuse.js for fuzzy search
            const fuseOptions = {
              keys: ['title', 'product_type', 'tags', 'vendor'],
              threshold: 0.3,
              includeScore: true
            };

            const fuse = new Fuse(products, fuseOptions);
            const searchResults = fuse.search(query);

            filteredProducts = searchResults.map(result => result.item);

            // If no fuzzy matches, fallback to simple includes search
            if (filteredProducts.length === 0) {
              const queryLower = query.toLowerCase();
              filteredProducts = products.filter(product =>
                product.title?.toLowerCase().includes(queryLower) ||
                product.product_type?.toLowerCase().includes(queryLower) ||
                product.tags?.toLowerCase().includes(queryLower) ||
                product.vendor?.toLowerCase().includes(queryLower)
              );
            }
          }

          // Limit results to reasonable number
          const limitedProducts = filteredProducts.slice(0, 20);

          // Format products for better readability
          const formattedProducts = limitedProducts.map(product => ({
            id: product.id,
            title: product.title,
            handle: product.handle,
            price: product.variants?.[0]?.price || 'N/A',
            compare_at_price: product.variants?.[0]?.compare_at_price || null,
            inventory_quantity: product.variants?.[0]?.inventory_quantity || 0,
            product_type: product.product_type,
            vendor: product.vendor,
            tags: product.tags,
            image: product.image?.src || product.images?.[0]?.src || null,
            description: product.body_html?.replace(/<[^>]*>/g, '').substring(0, 200) || '',
            status: product.status,
            created_at: product.created_at,
            updated_at: product.updated_at
          }));

          return {
            status: 'success',
            products: formattedProducts,
            total_found: filteredProducts.length,
            showing: limitedProducts.length,
            query: query || 'all products'
          };
        } catch (error) {
          console.error('Error in product_lookup tool:', error);
          return {
            status: 'error',
            message: `Error fetching products: ${error.message}`,
            products: []
          };
        }
      },

      'best_sellers': async (params: any): Promise<any> => {
        console.log('BEST SELLERS TOOL CALLED WITH:', params);
        console.log('BEST SELLERS TOOL - orgId:', params.orgId);

        try {
          const timeframe = params.timeframe || '30d';
          const limit = Math.min(params.limit || 10, 50);
          const orgId = params.orgId || 0;

          // Check if Shopify is connected for this organization
          const isShopifyConnected = await this.isShopifyConnected(orgId);

          if (!isShopifyConnected) {
            return {
              status: 'success',
              products: [],
              message: 'No sales data available - Shopify not connected'
            };
          }

          // For now, we'll return top products from the product catalog
          // In a real implementation, you'd query actual sales data
          const products = await this.fetchProductsFromShopify(orgId);

          if (!products || products.length === 0) {
            return {
              status: 'success',
              products: [],
              message: 'No products found in store'
            };
          }

          // Sort by created date as a proxy for popularity (newer products first)
          // In a real implementation, you'd sort by actual sales data
          const sortedProducts = products
            .filter(product => product.status === 'active')
            .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
            .slice(0, limit);

          const formattedProducts = sortedProducts.map((product, index) => ({
            id: product.id,
            title: product.title,
            handle: product.handle,
            price: product.variants?.[0]?.price || 'N/A',
            compare_at_price: product.variants?.[0]?.compare_at_price || null,
            inventory_quantity: product.variants?.[0]?.inventory_quantity || 0,
            product_type: product.product_type,
            vendor: product.vendor,
            image: product.image?.src || product.images?.[0]?.src || null,
            description: product.body_html?.replace(/<[^>]*>/g, '').substring(0, 200) || '',
            rank: index + 1,
            // Mock sales data - in real implementation, get from analytics
            sales_count: Math.floor(Math.random() * 100) + 10,
            revenue: Math.floor(Math.random() * 5000) + 500,
            timeframe: timeframe
          }));

          return {
            status: 'success',
            products: formattedProducts,
            timeframe: timeframe,
            total_products: sortedProducts.length,
            note: 'Sales data is simulated - integrate with actual analytics for real data'
          };
        } catch (error) {
          console.error('Error in best_sellers tool:', error);
          return {
            status: 'error',
            message: `Error fetching best sellers: ${error.message}`,
            products: []
          };
        }
      },

      'image_lookup': async (params: any): Promise<any> => {
        console.log('IMAGE LOOKUP TOOL CALLED WITH:', params);
        console.log('IMAGE LOOKUP TOOL - orgId:', params.orgId);

        try {
          const tag = params.tag?.trim();
          const description = params.description?.trim();
          const limit = params.limit || 10;
          const orgId = params.orgId || 0;
          console.log('IMAGE LOOKUP TOOL - using orgId:', orgId);

          // Fetch brand images with non-null and non-empty imageType
          const brandImages = await this.imageRepository.find({
            where: {
              orgId: orgId,
              imageType: {
                neq: '',
              }
            }
          });

          // Filter images that have AI tags
          let filteredImages = brandImages.filter(image =>
            image.imageType !== null &&
            image.imageType !== undefined &&
            image.imageType.trim() !== ''
          );

          if (filteredImages.length === 0) {
            return {
              status: 'success',
              images: [],
              message: 'No AI-tagged images found. Please add AI Tags to your images in the Assets section.',
              searchParams: { tag, description, limit }
            };
          }

          // First, attempt to filter by tag (partial match, case-insensitive) if provided
          let tagMatched = filteredImages;
          if (tag) {
            tagMatched = filteredImages.filter(image =>
              image.imageType?.trim().toLowerCase().includes(tag.toLowerCase())
            );
          }

          // Now, apply description filter if provided
          let results = tagMatched;
          let searchTerms: any[] = [];
          if (description) {
            searchTerms = description.toLowerCase().split(' ').filter((term: string) => term.length > 2);

            if (searchTerms.length > 0) {
              results = results.filter(image => {
                const imageDescription = (image.description || '').toLowerCase();
                const imageFriendlyname = (image.friendlyname || '').toLowerCase();
                // Check if any search terms match the image description or friendlyname
                return searchTerms.some((term: string) =>
                  imageDescription.includes(term) || imageFriendlyname.includes(term)
                );
              });
            }
          }

          // Fallback: If no results with tag but description is provided, search by description on all filtered images
          if (results.length === 0 && tag && description && searchTerms.length > 0) {
            console.log(`No matches found with tag "${tag}". Falling back to description search only.`);
            results = filteredImages.filter(image => {
              const imageDescription = (image.description || '').toLowerCase();
              const imageFriendlyname = (image.friendlyname || '').toLowerCase();
              return searchTerms.some((term: string) =>
                imageDescription.includes(term) || imageFriendlyname.includes(term)
              );
            });
          }

          // Sort by relevance and limit results
          const limitedImages = results.slice(0, limit);

          // Format the results
          const formattedImages = limitedImages.map((image, index) => ({
            rank: index + 1,
            url: image.url,
            imageType: image.imageType,
            title: image.friendlyname,
            description: image.description || 'No description available',
            width: image.width || 0,
            height: image.height || 0,
            contentType: image.contentType,
            displayText: `${image.imageType}: ${image.description || 'No description'}`,
			aspectRatio: calculateAspectRatio(image.width || 0, image.height || 0)
          }));


          console.log(`Image search found ${formattedImages.length} matches for tag: "${tag}", description: "${description}"`);

          return {
            status: 'success',
            images: formattedImages,
            totalImages: formattedImages.length,
            searchParams: { tag, description, limit }
          };

        } catch (error) {
          console.error('Error in image_lookup tool:', error);
          return {
            status: 'error',
            message: `Error fetching images: ${error.message}`,
            images: []
          };
        }
      },

      'image_edit': async (params: any): Promise<any> => {
        console.log('IMAGE EDIT TOOL CALLED WITH:', params);
        console.log('IMAGE EDIT TOOL - orgId:', params.orgId);

        try {
          const imageUrl = params.image_url;
          const newText = params.new_text;
          const textToReplace = params.text_to_replace;
          const modificationDescription = params.modification_description;
          const model = 'flux1'; // Default to Flux1
          const orgId = params.orgId || 0;

          if (!imageUrl) {
            return {
              status: 'error',
              message: 'Missing required parameter: image_url is required'
            };
          }

          // Determine edit type based on available parameters
          const isTextEdit = !!newText;
          const isModificationEdit = !!modificationDescription;
		  const removeText = !!textToReplace;

          // Validate that we have either text or modification parameters
          if (!isTextEdit && !isModificationEdit && !removeText) {
            return {
              status: 'error',
              message: 'Either new_text or modification_description is required'
            };
          }

          // Default to Flux1
          return await this.editImageFlux1(params);
        } catch (error) {
          console.error('Error in image_edit tool:', error);
          return {
            status: 'error',
            message: 'Failed to edit image',
            error: error.message,
            model: 'flux1'
          };
        }
      },

      'image_info': async (params: any): Promise<any> => {
        console.log('IMAGE INFO TOOL CALLED WITH:', params);
        console.log('IMAGE INFO TOOL - orgId:', params.orgId);

        try {
          const url = params.url;
          const orgId = params.orgId || 0;

          if (!url) {
            return {
              status: 'error',
              message: 'Missing required parameter: url is required'
            };
          }

          // Call the OpenAI service to analyze the image
          const result = await this.openAiService.getImageInfo(url);

          // Parse the JSON response
          let parsedResult;
          try {
            parsedResult = JSON.parse(result);
          } catch (parseError) {
            console.error('Error parsing image info response:', parseError);
            return {
              status: 'error',
              message: 'Failed to parse image analysis response',
              raw_response: result
            };
          }

          return {
            status: 'success',
            image_url: url,
            analysis: parsedResult,
            model: 'gpt-4o-mini'
          };
        } catch (error) {
          console.error('Error in image_info tool:', error);
          return {
            status: 'error',
            message: 'Failed to analyze image',
            error: error.message,
            image_url: params.url
          };
        }
      }
    };
  }

  /**
   * Edit image using Flux1 Kontext API
   */
  private async editImageFlux1(params: any): Promise<any> {
    const imageUrl = params.image_url;
    const newText = params.new_text;
    const textToReplace = params.text_to_replace;
    const modificationDescription = params.modification_description;
    const aspectRatio = params.aspect_ratio;
    const orgId = params.orgId || 0;

    // Determine edit type
    const isTextEdit = !!newText;
    const isModificationEdit = !!modificationDescription;
	const isRemoveText = !!textToReplace;

    // Prepare prompt for Flux1 (simpler structure)
    let prompt = '';
    if (isTextEdit && newText.trim()) {
      if (textToReplace) {
        prompt = `Replace "${textToReplace}" with "${newText}" on this image`;
      } else {
        prompt = `Add the text "${newText}" to this image`;
      }
    } else if(isRemoveText) {
		prompt = `Remove the text "${textToReplace}" from this image`;
	}
	else {
      prompt = modificationDescription;
    }

    try {
      // Detect aspect ratio if not provided
      let finalAspectRatio = aspectRatio;
      if (!finalAspectRatio) {
        try {
          const response = await fetch(imageUrl);
          const buffer = await response.arrayBuffer();
          const sharp = require('sharp');
          const metadata = await sharp(Buffer.from(buffer)).metadata();
          const width = metadata.width || 1024;
          const height = metadata.height || 1024;
          finalAspectRatio = calculateAspectRatio(width, height);
        } catch (error) {
          console.warn('Could not detect image dimensions, using 1:1 ratio:', error);
          finalAspectRatio = '1:1';
        }
      }

      // Use ChatService's editImageFlux1 method with aspect ratio
      const result = await this.chatService.editImageFlux1(prompt, imageUrl, finalAspectRatio);
      await result.completionPromise;

      const message = await this.messageRepository.findById(result.messageId);
      const { editedImages = [], aspectRatio: metadataAspectRatio } = this.parseLLMMetadata(message);

      if (editedImages && editedImages.length > 0) {
        // Upload all Flux1 images to S3 for permanent storage
        const s3Urls = await Promise.all(
          editedImages.map(async (img: any) => {
            return await this.uploadFlux1ImageToS3(img.url);
          })
        );

        console.log(`Successfully processed ${s3Urls.length} Flux1 edited image variations`);

        return {
          status: 'success',
		  prompt: prompt,
          edited_image_urls: s3Urls,
          original_url: imageUrl,
          edit_type: isTextEdit ? 'text' : 'modification',
          applied_text: isTextEdit ? newText : undefined,
          replaced_text: isTextEdit ? textToReplace : undefined,
          modification_description: isModificationEdit ? modificationDescription : undefined,
          model: 'flux1',
          variations_count: s3Urls.length,
          aspectRatio: metadataAspectRatio || finalAspectRatio // Use aspect ratio from metadata (if available) or the detected one
        };
      } else {
        return {
          status: 'error',
          message: 'Failed to edit image with Flux1',
          original_url: imageUrl
        };
      }
    } catch (error) {
      console.error('Error in Flux1 image edit:', error);
      return {
        status: 'error',
        message: 'Failed to edit image with Flux1',
        error: error.message,
        original_url: imageUrl
      };
    }
  }

  /**
   * Edit image using OpenAI GPT-4 Vision (original implementation)
   */
  private async editImageOpenAI(params: any): Promise<any> {
    const imageUrl = params.image_url;
    const newText = params.new_text;
    const textToReplace = params.text_to_replace;
    const modificationDescription = params.modification_description;
    const orgId = params.orgId || 0;

    // Determine edit type
    const isTextEdit = !!newText;
    const isModificationEdit = !!modificationDescription;

    // Detect the optimal size based on the original image
    const detectedSize = await this.detectImageSize(imageUrl);
    console.log('Detected image size:', detectedSize);

    // Prepare the prompt for image editing based on edit type
    let prompt = '';

    if (isTextEdit) {
      // Parse styling from the new text
      const { actualText, styling } = this.parseTextStyling(newText || '');
      console.log('Parsed text:', actualText);
      console.log('Parsed styling:', styling);

      if (textToReplace) {
        if (styling.length > 0) {
          prompt = `Change the text "${textToReplace}" to "${actualText}" on this image. Apply the following styling: ${styling.join(', ')}. Maintain the placement of the original text.`;
        } else {
          prompt = `Change the text "${textToReplace}" to "${actualText}" on this image. Maintain the style and placement of the original text.`;
        }
      } else {
        if (styling.length > 0) {
          prompt = `Add the text "${actualText}" to this image with the following styling: ${styling.join(', ')}. Place it in a visually appealing way that matches the image purpose.`;
        } else {
          prompt = `Add the text "${actualText}" to this image in a visually appealing way that matches the image style and purpose.`;
        }
      }
    } else {
      // Handle non-text modifications
      prompt = `Make the following modification to this image: ${modificationDescription}. Ensure the changes look natural and maintain the overall quality and coherence of the image.`;
    }

    try {
      // Helper function to convert URL to data URL
      const urlToDataUrl = async (url: string): Promise<string> => {
        const response = await fetch(url);
        const contentType = response.headers.get('content-type');
        if (!contentType) {
          throw new Error('No content-type in response');
        }
        const arrayBuffer = await response.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        return `data:${contentType};base64,${buffer.toString('base64')}`;
      };

      // Convert image URL to data URL
      const imageDataUrl = await urlToDataUrl(imageUrl);

      // Create model string that includes quality info
      const modelWithQuality = `gpt-image-1`;

      const { messageId, completionPromise } = await this.chatService.editImage(
        prompt,
        [imageDataUrl], // Only pass the converted data URL
        undefined, // No mask
        {
          models: [modelWithQuality],
          imageEditing: {
            imageDataUrls: [imageDataUrl],
            maskDataUrl: undefined,
            quality: 'standard',
            prompt: prompt,
            size: detectedSize,
            n: 3  // Generate 3 variations
          }
        }
      );

      // Wait for completion
      await completionPromise;

      const message = await this.messageRepository.findById(messageId);
      const { editedImages = [] } = this.parseLLMMetadata(message);

      if (editedImages && editedImages.length > 0) {
        // Upload all base64 images to S3 and get public URLs
        const uploadPromises = editedImages.map((img: any) => this.uploadBase64ToS3(img.url));
        const s3Urls = await Promise.all(uploadPromises);

        console.log(`Successfully processed ${s3Urls.length} OpenAI edited image variations`);

        return {
          status: 'success',
          edited_image_urls: s3Urls, // Return array of S3 URLs instead of base64
          original_url: imageUrl,
          edit_type: isTextEdit ? 'text' : 'modification',
          applied_text: isTextEdit ? newText : undefined,
          replaced_text: isTextEdit ? textToReplace : undefined,
          modification_description: isModificationEdit ? modificationDescription : undefined,
          detected_size: detectedSize,
          model: 'openai',
          variations_count: s3Urls.length
        };
      } else {
        return {
          status: 'error',
          message: 'Failed to edit image with OpenAI',
          original_url: imageUrl
        };
      }
    } catch (error) {
      console.error('Error in OpenAI image edit:', error);
      return {
        status: 'error',
        message: 'Failed to edit image with OpenAI',
        error: error.message,
        original_url: imageUrl
      };
    }
  }

  /**
   * Upload Flux1 image to S3 and return permanent URL
   */
  private async uploadFlux1ImageToS3(imageUrl: string): Promise<string> {
    try {
      // Fetch the image from Flux1 URL
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch image from Flux1: ${response.statusText}`);
      }

      const imageBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(imageBuffer);

      const AWS = require('aws-sdk');
      const s3 = new AWS.S3({
        accessKeyId: process.env.API_ACCESS_KEY,
        secretAccessKey: process.env.API_SECRET_KEY,
        region: 'us-east-1'
      });

      const filename = `flux1-edited-${Date.now()}-${Math.random().toString(36).substring(2)}.png`;

      // Upload to S3
      await s3.putObject({
        Bucket: 'raleon-images-cdn',
        Key: filename,
        Body: buffer,
        ContentType: 'image/png',
        ACL: 'public-read'
      }).promise();

      // Generate the S3 URL
      const s3Domain = 'd3q4ufbgs1i4ak.cloudfront.net';
      const s3Path = '/' + encodeURIComponent(filename);
      const s3Url = `https://${s3Domain}${s3Path}`;

      console.log('Uploaded Flux1 image to S3:', s3Url);
      return s3Url;
    } catch (error) {
      console.error('Error uploading Flux1 image to S3:', error);
      // Return original URL as fallback
      return imageUrl;
    }
  }

  /**
   * Helper function to upload base64 image to S3 and return public URL
   */
  private async uploadBase64ToS3(base64Url: string): Promise<string> {
    try {
      // Check if it's already a regular URL (not base64)
      if (!base64Url.startsWith('data:')) {
        return base64Url; // Return as-is if it's already a regular URL
      }

      const AWS = require('aws-sdk');
      const s3 = new AWS.S3({
        accessKeyId: process.env.API_ACCESS_KEY,
        secretAccessKey: process.env.API_SECRET_KEY,
        region: 'us-east-1'
      });

      // Extract base64 data from data URL
      const base64Data = base64Url.split(',')[1];
      if (!base64Data) {
        throw new Error('Invalid base64 data URL');
      }

      const imageBuffer = Buffer.from(base64Data, 'base64');
      const filename = `edited-${Date.now()}-${Math.random().toString(36).substring(2)}.png`;

      // Upload to S3
      await s3.putObject({
        Bucket: 'raleon-images-cdn',
        Key: filename,
        Body: imageBuffer,
        ContentType: 'image/png',
        ACL: 'public-read'
      }).promise();

      // Generate the S3 URL
      const s3Domain = 'd3q4ufbgs1i4ak.cloudfront.net';
      const s3Path = '/' + encodeURIComponent(filename);
      const s3Url = `https://${s3Domain}${s3Path}`;

      console.log('Uploaded edited image to S3:', s3Url);
      return s3Url;
    } catch (error) {
      console.error('Error uploading to S3:', error);
      // Return original URL as fallback
      return base64Url;
    }
  }

  /**
   * Check if Shopify is connected for the given organization
   */
  private async isShopifyConnected(orgId: number): Promise<boolean> {
    try {
      // Use the existing shopify API invoker to check connection
      const result = await this.shopifyApiInvoker.invokeAdminApi(
        orgId,
        '/admin/api/2023-10/shop.json',
        'GET'
      );
      return !!result; // If we get a result, connection is successful
    } catch (error) {
      console.log('Shopify not connected for org:', orgId, error.message);
      return false;
    }
  }

  /**
   * Fetch products from Shopify
   */
  private async fetchProductsFromShopify(orgId: number): Promise<any[]> {
    try {
      // Check cache first
      const cacheKey = `products_${orgId}`;
      const cachedProducts = this.productCache.get(cacheKey);
      if (cachedProducts) {
        console.log('Returning cached products for org:', orgId);
        return cachedProducts as any[];
      }

      // Fetch from Shopify API
      const result = await this.shopifyApiInvoker.invokeAdminApi(
        orgId,
        '/admin/api/2023-10/products.json?limit=250',
        'GET'
      );

      if (!result || !result.products) {
        console.log('Failed to fetch products from Shopify for org:', orgId);
        return [];
      }

      const products = result.products;

      // Cache the results
      this.productCache.set(cacheKey, products);

      return products;
    } catch (error) {
      console.error('Error fetching products from Shopify:', error);
      return [];
    }
  }

  /**
   * Helper function to parse styling from text
   */
  private parseTextStyling(text: string): { actualText: string; styling: string[] } {
    const styleKeywords = [
      // Colors
      'red', 'blue', 'green', 'yellow', 'orange', 'purple', 'pink', 'black', 'white', 'gray', 'grey',
      'gold', 'silver', 'brown', 'cyan', 'magenta', 'teal', 'navy', 'maroon', 'lime',
      // Styles
      'bold', 'italic', 'underlined', 'uppercase', 'lowercase', 'capitalized',
      // Sizes
      'large', 'small', 'huge', 'tiny', 'big', 'medium',
      // Fonts
      'serif', 'sans-serif', 'cursive', 'monospace', 'handwritten',
      // Effects
      'glowing', 'shadowed', 'outlined', 'gradient', '3d', 'neon', 'metallic'
    ];

    const words = text.split(' ');
    const styling: string[] = [];
    const actualWords: string[] = [];

    // Check each word to see if it's a styling keyword
    let foundTextKeyword = false;
    for (let i = 0; i < words.length; i++) {
      const word = words[i].toLowerCase();
      // Check if this word is followed by "text" or "font"
      if ((word === 'text' || word === 'font') && i > 0) {
        foundTextKeyword = true;
        continue; // Skip the word "text" or "font"
      }

      // If we haven't found the actual text yet and this is a style keyword
      if (!foundTextKeyword && styleKeywords.includes(word)) {
        styling.push(words[i]); // Keep original case for the style
      } else {
        // This is part of the actual text
        actualWords.push(words[i]);
        foundTextKeyword = true; // Everything after this is actual text
      }
    }

    // If no text was found after parsing, treat the whole thing as text
    if (actualWords.length === 0) {
      return { actualText: text, styling: [] };
    }

    return {
      actualText: actualWords.join(' '),
      styling: styling
    };
  }

  /**
   * Helper function to detect image aspect ratio using Sharp
   */
  private async detectImageSize(imageUrl: string): Promise<'1024x1024' | '1536x1024' | '1024x1536'> {
    try {
      const sharp = require('sharp');

      // Fetch the image
      const response = await fetch(imageUrl);
      if (!response.ok) {
        console.warn('Could not fetch image for size detection, using default');
        return '1024x1024';
      }

      // Get the image buffer
      const buffer = await response.arrayBuffer();
      const imageBuffer = Buffer.from(buffer);

      // Get image metadata using Sharp
      const metadata = await sharp(imageBuffer).metadata();
      const width = metadata.width || 1024;
      const height = metadata.height || 1024;
      const aspectRatio = width / height;

      console.log(`Original image dimensions: ${width}x${height}, aspect ratio: ${aspectRatio}`);

      // Determine best fit size based on aspect ratio
      if (Math.abs(aspectRatio - 1) < 0.1) {
        // Square or close to square
        return '1024x1024';
      } else if (aspectRatio > 1.3) {
        // Landscape
        return '1536x1024';
      } else if (aspectRatio < 0.8) {
        // Portrait
        return '1024x1536';
      } else {
        // Default to square for other ratios
        return '1024x1024';
      }
    } catch (error) {
      console.warn('Error detecting image size:', error);
      return '1024x1024';
    }
  }

  /**
   * Parse LLM metadata from message
   */
  private parseLLMMetadata(message: any): { editedImages?: any[]; aspectRatio?: string } {
    try {
      if (message?.llmMetadata && typeof message.llmMetadata === 'object') {
        return message.llmMetadata;
      }
      if (message?.llmMetadata && typeof message.llmMetadata === 'string') {
        return JSON.parse(message.llmMetadata);
      }
      return {};
    } catch (error) {
      console.warn('Error parsing LLM metadata:', error);
      return {};
    }
  }
}
