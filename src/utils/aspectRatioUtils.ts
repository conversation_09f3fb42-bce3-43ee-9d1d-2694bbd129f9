// Predefined aspect ratios with their labels and numerical values
export const ASPECT_RATIOS = [
  { label: '3:7', value: 0.43, description: 'Tall portrait' },
  { label: '9:19', value: 0.47, description: 'Tall portrait' },
  { label: '1:2', value: 0.50, description: 'Portrait' },
  { label: '9:16', value: 0.56, description: 'Portrait (common mobile)' },
  { label: '2:3', value: 0.67, description: 'Portrait' },
  { label: '3:4', value: 0.75, description: 'Portrait' },
  { label: '4:5', value: 0.80, description: 'Portrait' },
  { label: '1:1', value: 1.00, description: 'Square' },
  { label: '5:4', value: 1.25, description: 'Landscape' },
  { label: '4:3', value: 1.33, description: 'Landscape' },
  { label: '3:2', value: 1.50, description: 'Landscape' },
  { label: '16:9', value: 1.78, description: 'Landscape (common widescreen)' },
  { label: '2:1', value: 2.00, description: 'Wide landscape' },
  { label: '19:9', value: 2.11, description: 'Wide landscape' },
  { label: '7:3', value: 2.33, description: 'Ultra-wide landscape' }
];

/**
 * Finds the closest predefined aspect ratio to the given value
 * @param ratio - The calculated aspect ratio (width/height)
 * @returns The closest matching aspect ratio object
 */
export function getClosestAspectRatio(ratio: number): { label: string; value: number; description: string } {
  if (isNaN(ratio) || ratio <= 0) {
    return ASPECT_RATIOS[7]; // Default to 1:1 square
  }

  let closest = ASPECT_RATIOS[0];
  let minDifference = Math.abs(ratio - closest.value);

  for (const aspect of ASPECT_RATIOS) {
    const difference = Math.abs(ratio - aspect.value);
    if (difference < minDifference) {
      minDifference = difference;
      closest = aspect;
    }
  }

  return closest;
}

/**
 * Calculates the aspect ratio of an image from its width and height
 * @param width - The width of the image
 * @param height - The height of the image
 * @returns The aspect ratio label (e.g., "16:9")
 */
export function calculateAspectRatio(width: number, height: number): string {
  if (width <= 0 || height <= 0) {
    return '1:1'; // Default to square for invalid dimensions
  }

  const ratio = width / height;
  return getClosestAspectRatio(ratio).label;
}
