import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {DevDbDataSource} from '../datasources';
import {Message, MessageRelations, Conversation, User} from '../models';
import {ConversationRepository} from './conversation.repository';
import {UserRepository} from './user.repository';

export class MessageRepository extends DefaultCrudRepository<
	Message,
	typeof Message.prototype.id,
	MessageRelations
> {

	public readonly conversation: BelongsToAccessor<Conversation, typeof Message.prototype.id>;

	public readonly createdByUser: BelongsToAccessor<User, typeof Message.prototype.id>;

	constructor(
		@inject('datasources.dev_db') dataSource: DevDbDataSource, @repository.getter('ConversationRepository') protected conversationRepositoryGetter: Getter<ConversationRepository>, @repository.getter('UserRepository') protected userRepositoryGetter: Getter<UserRepository>,
	) {
		super(Message, dataSource);
		this.conversation = this.createBelongsToAccessorFor('conversation', conversationRepositoryGetter,);
		this.registerInclusionResolver('conversation', this.conversation.inclusionResolver);
		this.createdByUser = this.createBelongsToAccessorFor('createdByUser', userRepositoryGetter,);
		this.registerInclusionResolver('createdByUser', this.createdByUser.inclusionResolver);
	}
}
