<template>
  <div v-if="conversation" class="bg-white border-b border-gray-200 px-4 py-3">
    <div class="flex items-center justify-between">
      <!-- Conversation Title and Metadata -->
      <div class="flex-1 min-w-0">
        <h1 class="text-lg font-semibold text-gray-900 truncate">
          {{ conversation.name || 'Untitled Conversation' }}
        </h1>
        <div class="mt-1 flex items-center text-sm text-gray-500 space-x-3">
          <!-- Created by info -->
          <span v-if="conversation.createdByUser" class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Created by {{ formatUserName(conversation.createdByUser) }}
          </span>

          <!-- Created date -->
          <span v-if="conversation.createdAt" class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            {{ formatDate(conversation.createdAt) }}
          </span>

          <!-- Message count -->
          <span v-if="conversation.messageCount !== undefined" class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            {{ conversation.messageCount }} {{ conversation.messageCount === 1 ? 'message' : 'messages' }}
          </span>
        </div>
      </div>

      <!-- Action buttons slot -->
      <div v-if="$slots.actions" class="ml-4 flex-shrink-0">
        <slot name="actions"></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

interface ConversationUser {
  id: number;
  email: string;
  firstName?: string;
  lastName?: string;
}

interface ConversationInfo {
  id: number;
  name?: string;
  createdAt: string;
  updatedAt?: string;
  messageCount?: number;
  createdByUser?: ConversationUser;
}

export default defineComponent({
  name: 'ConversationHeader',
  props: {
    conversation: {
      type: Object as PropType<ConversationInfo>,
      required: true
    }
  },
  methods: {
    formatUserName(user?: ConversationUser): string {
      if (!user) return 'Unknown User';

      const firstName = user.firstName?.trim();
      const lastName = user.lastName?.trim();

      if (firstName && lastName) {
        return `${firstName} ${lastName}`;
      } else if (firstName) {
        return firstName;
      } else if (lastName) {
        return lastName;
      } else {
        // Fallback to email if no name is available
        return user.email;
      }
    },

    formatDate(dateString: string): string {
      const date = new Date(dateString);
      const now = new Date();
      const diffInMs = now.getTime() - date.getTime();
      const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

      if (diffInDays === 0) {
        return 'Today';
      } else if (diffInDays === 1) {
        return 'Yesterday';
      } else if (diffInDays < 7) {
        return `${diffInDays} days ago`;
      } else {
        return date.toLocaleDateString();
      }
    }
  }
});
</script>

<style scoped>
/* Add any specific styles if needed */
</style>
