<template>
  <div class="flex flex-col h-full bg-[#F5F5F5]">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex-1 flex justify-center items-center">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#6E41FF]"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="loadError" class="flex-1 flex flex-col justify-center items-center p-4">
      <div class="text-red-500 mb-4">{{ loadError }}</div>
      <button
        @click="loadConversation(currentConversationId)"
        class="px-4 py-2 bg-[#6e3ff2] text-white rounded-md hover:bg-[#5a32d4] transition-colors duration-200"
      >
        Try Again
      </button>
    </div>

    <!-- Conversation Header -->
    <ConversationHeader v-if="!isLoading && !loadError && conversation" :conversation="conversation" />

    <!-- Chat Messages Area -->
    <div v-if="!isLoading && !loadError" class="flex-1 overflow-y-auto p-4 space-y-4" ref="messagesContainer">
      <template v-for="message in messages" :key="message.messageId || message.timestamp">
        <div :class="[
          'max-w-3/4 rounded-2xl p-4',
          message.role === 'user'
            ? 'bg-ralprimary-main text-white ml-auto'
            : 'bg-white border border-gray-200'
        ]">
          <div class="whitespace-pre-wrap mb-2">{{ message.content }}</div>

          <!-- Tool Calls Section -->
          <template v-if="message.toolCalls">
            <div class="mt-3 pt-3 border-t border-gray-200 space-y-2">
              <div v-for="(toolCall, index) in parseToolCalls(message.toolCalls)" :key="index"
                   class="bg-blue-50 rounded-lg p-3 text-sm">
                <div class="font-medium text-blue-700">🛠️ Tool Called: {{ toolCall.function?.name }}</div>
                <div class="mt-1 text-gray-600">
                  <pre class="whitespace-pre-wrap">{{ JSON.stringify(toolCall.function?.arguments || {}, null, 2) }}</pre>
                </div>
                <!-- Tool Results -->
                <div v-if="message.toolResults" class="mt-2 pt-2 border-t border-gray-300">
                  <div class="font-medium text-green-700">📤 Result:</div>
                  <pre class="whitespace-pre-wrap text-gray-600">{{
                    getToolResult(message.toolResults, index)
                  }}</pre>
                </div>
              </div>
            </div>
          </template>

          <div class="text-xs opacity-70 mt-2">
            <span>{{ new Date(message.timestamp).toLocaleTimeString() }}</span>
            <span v-if="message.messageId" class="ml-2">#{{ message.messageId }}</span>
          </div>
        </div>
      </template>
      <div v-if="isStreaming"
           class="max-w-3/4 rounded-2xl p-4 bg-white border border-gray-200">
        <div class="whitespace-pre-wrap mb-2">{{ streamingContent }}</div>

        <!-- Streaming Tool Calls -->
        <template v-if="streamingToolCalls">
          <div class="mt-3 pt-3 border-t border-gray-200 space-y-2">
            <div v-for="(toolCall, index) in parseToolCalls(streamingToolCalls)" :key="index"
                 class="bg-blue-50 rounded-lg p-3 text-sm">
              <div class="font-medium text-blue-700">🛠️ Tool Called: {{ toolCall.function?.name }}</div>
              <div class="mt-1 text-gray-600">
                <pre class="whitespace-pre-wrap">{{ JSON.stringify(toolCall.function?.arguments || {}, null, 2) }}</pre>
              </div>
              <!-- Streaming Tool Results -->
              <div v-if="streamingToolResults" class="mt-2 pt-2 border-t border-gray-300">
                <div class="font-medium text-green-700">📤 Result:</div>
                <pre class="whitespace-pre-wrap text-gray-600">{{
                  getToolResult(streamingToolResults, index)
                }}</pre>
              </div>
            </div>
          </div>
        </template>

        <div class="text-xs text-gray-500 mt-2">
          <span>{{ streamingStatus }}</span>
          <span v-if="currentMessageId" class="ml-2">#{{ currentMessageId }}</span>
        </div>
      </div>
    </div>

    <!-- Test Tool Call Button and Input Area -->
    <div v-if="!isLoading && !loadError" class="border-t border-gray-200 p-4 bg-white">
      <button
        @click="testToolCall"
        class="mb-4 w-full bg-green-100 text-green-700 px-4 py-2 rounded-lg hover:bg-green-200"
        :disabled="isStreaming"
      >
        Test Tool Call
      </button>

      <!-- Input Area -->
      <form @submit.prevent="sendMessage" class="flex gap-2">
        <input
          v-model="newMessage"
          type="text"
          placeholder="Type your message..."
          class="flex-1 rounded-lg border border-gray-300 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-ralprimary-light"
          :disabled="isStreaming"
        >
        <button
          type="submit"
          class="bg-ralprimary-main text-white px-6 py-2 rounded-lg hover:bg-ralprimary-dark disabled:opacity-50"
          :disabled="!newMessage.trim() || isStreaming"
        >
          Send
        </button>
      </form>
    </div>
  </div>
</template>

<script>
import * as Utils from '../../client-old/utils/Utils';
import ConversationHeader from './ConversationHeader.ts.vue';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
  name: 'Chat',
  components: {
    ConversationHeader
  },
  data() {
    return {
      messages: [],
      newMessage: '',
      currentConversationId: null,
      currentMessageId: null,
      conversation: null, // Store full conversation data
      isStreaming: false,
      streamingContent: '',
      streamingStatus: null,
      streamingToolCalls: null,
      streamingToolResults: null,
      isLoading: false,
      loadError: null,
    };
  },
  mounted() {
    // Check if there's a conversation_id in the URL query parameters
    const urlParams = new URLSearchParams(window.location.search);
    const conversationId = urlParams.get('conversation_id');

    if (conversationId) {
      this.loadConversation(conversationId);
    }
  },
  methods: {
    async loadConversation(conversationId) {
      this.isLoading = true;
      this.loadError = null;

      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${URL_DOMAIN}/chat/conversations/${conversationId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to load conversation');
        }

        const conversation = await response.json();
        this.currentConversationId = conversation.id;
        this.conversation = conversation; // Store full conversation data

        // Convert messages to the format expected by the UI
        if (conversation.messages && conversation.messages.length > 0) {
          this.messages = conversation.messages.map(msg => ({
            role: msg.role,
            content: msg.content,
            messageId: msg.id,
            timestamp: msg.createdAt,
            toolCalls: msg.toolCalls,
            toolResults: msg.toolResults
          }));
        }

        // Scroll to bottom after messages are loaded
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      } catch (error) {
        console.error('Error loading conversation:', error);
        this.loadError = 'Failed to load conversation. Please try again.';
      } finally {
        this.isLoading = false;
      }
    },
    async sendMessage() {
      const message = this.newMessage.trim();
      if (!message || this.isStreaming) return;

      this.newMessage = '';

      try {
        if (!this.currentConversationId) {
          // Start new conversation
          await this.startConversation(message);
        } else {
          // Continue existing conversation
          await this.continueConversation(message);
        }
      } catch (error) {
        console.error('Failed to send message:', error);
        // Add error handling UI if needed
      }
    },

    async startConversation(message) {
      const response = await fetch(`${URL_DOMAIN}/chat/conversations/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          message,
          stream: true
        })
      });

      if (!response.ok) throw new Error('Failed to start conversation');

      // Add user message immediately
      this.messages.push({
        role: 'user',
        content: message,
        timestamp: new Date().toISOString()
      });

      this.isStreaming = true;
      this.streamingContent = '';
      this.streamingStatus = 'started';

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const {value, done} = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, {stream: true});
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim() && line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(5));
                this.handleStreamingData(data);
              } catch (e) {
                console.error('Failed to parse SSE data:', e);
              }
            }
          }
        }
      } finally {
        if (buffer) {
          const line = buffer.trim();
          if (line && line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(5));
              this.handleStreamingData(data);
            } catch (e) {
              console.error('Failed to parse final SSE data:', e);
            }
          }
        }

        this.isStreaming = false;
        this.finalizeStreamingMessage();
      }
    },

    async continueConversation(message) {
      const response = await fetch(`${URL_DOMAIN}/chat/conversations/${this.currentConversationId}/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          message,
          stream: true
        })
      });

      if (!response.ok) throw new Error('Failed to continue conversation');

      // Add user message immediately
      this.messages.push({
        role: 'user',
        content: message,
        timestamp: new Date().toISOString()
      });

      this.isStreaming = true;
      this.streamingContent = '';
      this.streamingStatus = 'started';

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const {value, done} = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, {stream: true});
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim() && line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(5));
                this.handleStreamingData(data);
              } catch (e) {
                console.error('Failed to parse SSE data:', e);
              }
            }
          }
        }
      } finally {
        if (buffer) {
          const line = buffer.trim();
          if (line && line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(5));
              this.handleStreamingData(data);
            } catch (e) {
              console.error('Failed to parse final SSE data:', e);
            }
          }
        }

        this.isStreaming = false;
        this.finalizeStreamingMessage();
      }
    },

    scrollToBottom() {
      const container = this.$refs.messagesContainer;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },

    parseToolCalls(toolCalls) {
      try {
        return typeof toolCalls === 'string' ? JSON.parse(toolCalls) : toolCalls;
      } catch (e) {
        console.error('Failed to parse tool calls:', e);
        return [];
      }
    },

    getToolResult(toolResults, index) {
      try {
        const results = typeof toolResults === 'string' ? JSON.parse(toolResults) : toolResults;
        return JSON.stringify(results[index], null, 2);
      } catch (e) {
        console.error('Failed to parse tool results:', e);
        return '';
      }
    },

    async testToolCall() {
      const testMessage = "Use the semantic_search tool to find files containing the word 'dashboard' in the codebase.";
      this.newMessage = testMessage;
      await this.sendMessage();
    },

    // Update the parsing of streaming data in startConversation and continueConversation
    handleStreamingData(data) {
      if (data.conversationId && !this.currentConversationId) {
        this.currentConversationId = data.conversationId;
      }
      if (data.messageId && !this.currentMessageId) {
        this.currentMessageId = data.messageId;
      }
      if (data.content) {
        this.streamingContent += data.content;
      }
      if (data.status) {
        this.streamingStatus = data.status;
      }
      // Update streaming tool calls and results
      if (data.toolCalls) {
        this.streamingToolCalls = data.toolCalls;
      }
      if (data.toolResults) {
        this.streamingToolResults = data.toolResults;
      }
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },

    finalizeStreamingMessage() {
      this.messages.push({
        role: 'assistant',
        content: this.streamingContent,
        messageId: this.currentMessageId,
        timestamp: new Date().toISOString(),
        toolCalls: this.streamingToolCalls,
        toolResults: this.streamingToolResults
      });
      this.streamingContent = '';
      this.streamingStatus = null;
      this.streamingToolCalls = null;
      this.streamingToolResults = null;
      this.currentMessageId = null;
    }
  }
};
</script>

<style scoped>
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

/* Add styles for tool calls */
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  overflow-x: auto;
}
</style>
